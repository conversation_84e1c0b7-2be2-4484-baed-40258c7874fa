/**
 * API接口统一管理
 */

import request from '@/utils/request'
import type { ApiResponse } from '@/utils/request'

// 电力系统数据接口
export interface PowerSystemData {
  total: string
  unit: {
    type: string[]
    axis: string[]
    data: string[][]
  }
}

// 光伏系统数据接口
export interface PhotovoltaicData {
  total: string
  apparent: string
  reactive: string
  electricity: {
    type: string[]
    axis: string[]
    data: string[][]
  }
}

// 设备统计数据接口
export interface DeviceStatisticsData {
  [key: string]: any
}

// 能耗数据接口
export interface EnergyDataItem {
  park: string  // 园区总能耗
  unit: string  // 单位面积能耗
}

export interface EnergyData {
  data: EnergyDataItem
}

// 地源热泵系统数据接口
export interface HeatPumpData {
  [key: string]: any
}

/**
 * 获取电力系统数据
 * @param type 电流: current 电压: voltage
 */
export const getPowerSystemData = (type: string): Promise<ApiResponse<PowerSystemData>> => {
  return request.get(`/cabin/power/${type}`)
}

/**
 * 获取光伏系统数据
 */
export const getPhotovoltaicData = (): Promise<ApiResponse<PhotovoltaicData>> => {
  return request.get('/cabin/photovoltaic')
}

/**
 * 获取设备统计数据
 */
export const getDeviceStatistics = (): Promise<ApiResponse<DeviceStatisticsData>> => {
  return request.get('/cabin/device')
}

/**
 * 获取能耗数据
 */
export const getEnergyData = (): Promise<ApiResponse<EnergyData>> => {
  return request.get('/cabin/energy')
}

/**
 * 获取能耗总览数据
 * @param dimension 时间维度: day(日) month(月) year(年)
 */
export const getEnergyOverview = (dimension: string): Promise<ApiResponse<any>> => {
  return request.get(`/cabin/energy/${dimension}`)
}

/**
 * 获取地源热泵系统数据
 * @param type 电流: current 电压: voltage
 */
export const getHeatPumpData = (type: string): Promise<ApiResponse<HeatPumpData>> => {
  return request.get(`/cabin/heatPump/${type}`)
}

// 导出所有API
export default {
  getPowerSystemData,
  getPhotovoltaicData,
  getDeviceStatistics,
  getEnergyData,
  getEnergyOverview,
  getHeatPumpData
}
